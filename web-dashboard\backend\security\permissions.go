package security

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"web-dashboard/backend/database"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/session"
)

type Permission struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

func GetAllPermissions() ([]Permission, error) {
	query := `SELECT id, name, description, resource, action, created_at, updated_at FROM permissions ORDER BY name`
	rows, err := database.DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query permissions: %v", err)
	}
	defer rows.Close()

	var permissions []Permission
	for rows.Next() {
		var permission Permission
		if err := rows.Scan(&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to scan permission: %v", err)
		}
		permissions = append(permissions, permission)
	}
	return permissions, nil
}

func CreatePermission(name, description, resource, action string) (*Permission, error) {
	query := `INSERT INTO permissions (name, description, resource, action) VALUES ($1, $2, $3, $4) RETURNING id, name, description, resource, action, created_at, updated_at`
	var permission Permission
	err := database.DB.QueryRow(query, name, description, resource, action).Scan(
		&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create permission: %v", err)
	}
	return &permission, nil
}

func GetPermission(id int) (*Permission, error) {
	query := `SELECT id, name, description, resource, action, created_at, updated_at FROM permissions WHERE id = $1`
	var permission Permission
	err := database.DB.QueryRow(query, id).Scan(
		&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %v", err)
	}
	return &permission, nil
}

func DeletePermission(id int) error {
	query := `DELETE FROM permissions WHERE id = $1`
	result, err := database.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete permission: %v", err)
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}
	if rowsAffected == 0 {
		return fmt.Errorf("permission not found")
	}
	return nil
}

func GetUserPermissions(userID int) ([]Permission, error) {
	query := `
		SELECT DISTINCT p.id, p.name, p.description, p.resource, p.action, p.created_at, p.updated_at
		FROM permissions p
		JOIN security_group_permissions sgp ON p.id = sgp.permission_id
		JOIN user_security_groups usg ON sgp.security_group_id = usg.security_group_id
		WHERE usg.user_id = $1
	`
	rows, err := database.DB.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %v", err)
	}
	defer rows.Close()

	var permissions []Permission
	for rows.Next() {
		var permission Permission
		if err := rows.Scan(&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action, &permission.CreatedAt, &permission.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to scan permission: %v", err)
		}
		permissions = append(permissions, permission)
	}
	return permissions, nil
}

func GetUserSecurityGroups(userID int) ([]SecurityGroup, error) {
	query := `
		SELECT sg.id, sg.name, sg.description, sg.created_at, sg.updated_at
		FROM security_groups sg
		JOIN user_security_groups usg ON sg.id = usg.security_group_id
		WHERE usg.user_id = $1
	`
	rows, err := database.DB.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user security groups: %v", err)
	}
	defer rows.Close()

	var groups []SecurityGroup
	for rows.Next() {
		var group SecurityGroup
		if err := rows.Scan(&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt); err != nil {
			return nil, fmt.Errorf("failed to scan security group: %v", err)
		}
		groups = append(groups, group)
	}
	return groups, nil
}

func UserHasPermission(userID int, resource, action string) (bool, error) {
	// SECURITY: Use centralized permission validation that includes super admin checks
	debug.Debug("UserHasPermission called for user %d, resource: %s, action: %s", userID, resource, action)

	// Check if user is in Super Admin group first
	isInSuperAdmin, err := isUserInSuperAdminGroup(userID)
	if err != nil {
		debug.Error("Failed to check Super Admin membership for user %d: %v", userID, err)
		return false, fmt.Errorf("failed to verify admin status: %v", err)
	}

	// If user is Super Admin, grant access immediately
	if isInSuperAdmin {
		debug.Debug("User %d is in Super Admin group, granting permission for %s:%s", userID, resource, action)
		return true, nil
	}

	// Check for specific resource/action permission in database
	query := `
		SELECT COUNT(*)
		FROM permissions p
		JOIN security_group_permissions sgp ON p.id = sgp.permission_id
		JOIN user_security_groups usg ON sgp.security_group_id = usg.security_group_id
		WHERE usg.user_id = $1 AND p.resource = $2 AND p.action = $3
	`
	var count int
	err = database.DB.QueryRow(query, userID, resource, action).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check user permission: %v", err)
	}

	hasPermission := count > 0
	debug.Debug("User %d permission check for %s:%s = %t", userID, resource, action, hasPermission)
	return hasPermission, nil
}

// UserHasPermissionByName checks if a user has a specific permission by name
// This is used for session-based permission validation
func UserHasPermissionByName(userID int, permissionName string) (bool, error) {
	// Use the centralized validation system
	result, err := ValidateUserPermissions(userID, []string{permissionName})
	if err != nil {
		return false, err
	}
	return result.HasPermission, nil
}

func EnsureUserInDefaultGroup(userID int, email string) error {
	debug.Debug("Checking security groups for user ID %d (%s)", userID, email)

	// Check if user is already in any security group
	var count int
	err := database.DB.QueryRow("SELECT COUNT(*) FROM user_security_groups WHERE user_id = $1", userID).Scan(&count)
	if err != nil {
		debug.Error("Failed to check user security groups for user %s: %v", email, err)
		return fmt.Errorf("failed to check user security groups: %v", err)
	}

	// If user is not in any security group, add them to the default "User" security group
	if count == 0 {
		debug.Info("User %s (ID: %d) has no security groups, adding to default User group", email, userID)
		// Get the "User" security group ID
		var groupID int
		err = database.DB.QueryRow("SELECT id FROM security_groups WHERE name = 'User'").Scan(&groupID)
		if err != nil {
			return fmt.Errorf("failed to get User security group: %v", err)
		}

		// Add user to the security group
		err = AddUserToSecurityGroup(userID, groupID)
		if err != nil {
			debug.Error("Failed to add user %s to User security group: %v", email, err)
			return fmt.Errorf("failed to add user to User security group: %v", err)
		}
		debug.Info("Successfully added user %s to User security group", email)
	} else {
		debug.Debug("User %s already belongs to %d security group(s)", email, count)
	}

	return nil
}

// PermissionValidationResult represents the result of a permission check
type PermissionValidationResult struct {
	HasPermission bool
	Reason        string
	IsSuperAdmin  bool
}

// ValidateUserPermissions is the centralized permission validation function
// SECURITY: This function provides a single, secure way to validate permissions
// It handles super admin checks, wildcard permissions, and specific permissions
func ValidateUserPermissions(userID int, requiredPermissions []string) (*PermissionValidationResult, error) {
	debug.Debug("Validating permissions for user ID %d, required: %v", userID, requiredPermissions)

	// SECURITY: Fail closed if no permissions required (shouldn't happen)
	if len(requiredPermissions) == 0 {
		return &PermissionValidationResult{
			HasPermission: false,
			Reason:        "No permissions specified",
			IsSuperAdmin:  false,
		}, nil
	}

	// Get user permissions with caching
	userPermissions, isInSuperAdmin, err := getUserPermissionsWithCache(userID)
	if err != nil {
		debug.Error("Failed to get permissions for user %d: %v", userID, err)
		return nil, fmt.Errorf("failed to get user permissions: %v", err)
	}

	// If user is Super Admin, grant access immediately
	if isInSuperAdmin {
		debug.Debug("User %d is in Super Admin group, granting access", userID)
		return &PermissionValidationResult{
			HasPermission: true,
			Reason:        "Super Admin access",
			IsSuperAdmin:  true,
		}, nil
	}

	debug.Debug("User %d has permissions: %v", userID, userPermissions)

	// Check for wildcard permission
	for _, userPerm := range userPermissions {
		if userPerm == "*" {
			debug.Debug("User %d has wildcard permission, granting access", userID)
			return &PermissionValidationResult{
				HasPermission: true,
				Reason:        "Wildcard permission",
				IsSuperAdmin:  false,
			}, nil
		}
	}

	// Check for specific permissions using exact matching
	for _, requiredPerm := range requiredPermissions {
		if slices.Contains(userPermissions, requiredPerm) {
			debug.Debug("User %d has required permission: %s", userID, requiredPerm)
			return &PermissionValidationResult{
				HasPermission: true,
				Reason:        fmt.Sprintf("Specific permission: %s", requiredPerm),
				IsSuperAdmin:  false,
			}, nil
		}
	}

	// SECURITY: Default to denial if no permissions match
	debug.Debug("User %d lacks required permissions", userID)
	return &PermissionValidationResult{
		HasPermission: false,
		Reason:        "Insufficient permissions",
		IsSuperAdmin:  false,
	}, nil
}

// isUserInSuperAdminGroup checks if a user is in the Super Admin security group
func isUserInSuperAdminGroup(userID int) (bool, error) {
	query := `
		SELECT COUNT(*)
		FROM user_security_groups usg
		JOIN security_groups sg ON usg.security_group_id = sg.id
		WHERE usg.user_id = $1 AND sg.name = 'Super Admin'
	`
	var count int
	err := database.DB.QueryRow(query, userID).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check Super Admin membership: %v", err)
	}
	return count > 0, nil
}

// getUserPermissionsFromDB gets all permission names for a user from the database
func getUserPermissionsFromDB(userID int) ([]string, error) {
	query := `
		SELECT DISTINCT p.name
		FROM permissions p
		JOIN security_group_permissions sgp ON p.id = sgp.permission_id
		JOIN user_security_groups usg ON sgp.security_group_id = usg.security_group_id
		WHERE usg.user_id = $1
	`
	rows, err := database.DB.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %v", err)
	}
	defer rows.Close()

	var permissions []string
	for rows.Next() {
		var permissionName string
		if err := rows.Scan(&permissionName); err != nil {
			return nil, fmt.Errorf("failed to scan permission: %v", err)
		}
		permissions = append(permissions, permissionName)
	}

	return permissions, nil
}

// CachedUserPermissions represents cached user permission data
type CachedUserPermissions struct {
	Permissions  []string  `json:"permissions"`
	IsSuperAdmin bool      `json:"is_super_admin"`
	CachedAt     time.Time `json:"cached_at"`
	ExpiresAt    time.Time `json:"expires_at"`
}

// getUserPermissionsWithCache gets user permissions with Redis caching
func getUserPermissionsWithCache(userID int) ([]string, bool, error) {
	// Try to get from cache first
	cacheKey := fmt.Sprintf("user_permissions:%d", userID)

	if session.Manager != nil && session.Manager.Client != nil {
		cachedData, err := session.Manager.Client.Get(session.Manager.Client.Context(), cacheKey).Result()
		if err == nil {
			var cached CachedUserPermissions
			if json.Unmarshal([]byte(cachedData), &cached) == nil {
				// Check if cache is still valid
				if time.Now().Before(cached.ExpiresAt) {
					debug.Debug("Using cached permissions for user %d", userID)
					return cached.Permissions, cached.IsSuperAdmin, nil
				}
			}
		}
	}

	// Cache miss or expired, get from database
	debug.Debug("Cache miss for user %d permissions, querying database", userID)

	// Check super admin status
	isSuperAdmin, err := isUserInSuperAdminGroup(userID)
	if err != nil {
		return nil, false, fmt.Errorf("failed to check super admin status: %v", err)
	}

	var permissions []string
	if isSuperAdmin {
		// Super admins get wildcard permission
		permissions = []string{"*"}
	} else {
		// Get specific permissions from database
		permissions, err = getUserPermissionsFromDB(userID)
		if err != nil {
			return nil, false, fmt.Errorf("failed to get user permissions: %v", err)
		}
	}

	// Cache the result for 5 minutes
	if session.Manager != nil && session.Manager.Client != nil {
		cached := CachedUserPermissions{
			Permissions:  permissions,
			IsSuperAdmin: isSuperAdmin,
			CachedAt:     time.Now(),
			ExpiresAt:    time.Now().Add(5 * time.Minute),
		}

		cachedData, err := json.Marshal(cached)
		if err == nil {
			session.Manager.Client.Set(session.Manager.Client.Context(), cacheKey, cachedData, 5*time.Minute)
			debug.Debug("Cached permissions for user %d", userID)
		}
	}

	return permissions, isSuperAdmin, nil
}

// InvalidateUserPermissionCache invalidates the permission cache for a user
func InvalidateUserPermissionCache(userID int) {
	if session.Manager != nil && session.Manager.Client != nil {
		cacheKey := fmt.Sprintf("user_permissions:%d", userID)
		session.Manager.Client.Del(session.Manager.Client.Context(), cacheKey)
		debug.Debug("Invalidated permission cache for user %d", userID)
	}
}
