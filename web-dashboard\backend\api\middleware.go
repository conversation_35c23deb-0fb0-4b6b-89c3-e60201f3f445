package api

import (
	"encoding/json"
	"net/http"

	"web-dashboard/backend/auth"
	"web-dashboard/backend/debug"
	"web-dashboard/backend/security"
	"web-dashboard/backend/session"
)

// checkSessionPermission is a utility function to check if a user has specific permissions
// SECURITY: This function uses the centralized permission validation system
func checkSessionPermission(sessionData *session.SessionData, requiredPermissions ...string) bool {
	// SECURITY: Fail closed if session data is invalid
	if sessionData == nil {
		debug.Debug("Session data is nil - denying access")
		return false
	}

	// Use the centralized permission validation system
	result, err := security.ValidateUserPermissions(sessionData.UserID, requiredPermissions)
	if err != nil {
		debug.Error("Permission validation error for user %s: %v", sessionData.Email, err)
		return false
	}

	debug.Debug("Permission check for user %s: %t (reason: %s)", sessionData.Email, result.HasPermission, result.Reason)
	return result.HasPermission
}

// requirePermissions creates middleware that checks for specific permissions
func requirePermissions(permissions ...string) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Check if session data exists in context
			sessionValue := r.Context().Value(auth.SessionContextKey)
			if sessionValue == nil {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{
					"error":   "unauthorized",
					"message": "Authentication required",
				})
				return
			}

			sessionData, ok := sessionValue.(*session.SessionData)
			if !ok {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				json.NewEncoder(w).Encode(map[string]string{
					"error":   "unauthorized",
					"message": "Invalid session data",
				})
				return
			}

			if !checkSessionPermission(sessionData, permissions...) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusForbidden)
				json.NewEncoder(w).Encode(map[string]string{
					"error":   "forbidden",
					"message": "Insufficient permissions",
				})
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// Common permission middleware functions
var (
	RequireSystemSettings          = requirePermissions("System Settings - View", "System Settings - Manage Email", "System Settings - Full Access")
	RequireUserManagement          = requirePermissions("User Management - View", "User Management - Activate", "User Management - Enable", "User Management - Delete", "Admin")
	RequireRedisManagement         = requirePermissions("Redis Management - View", "Redis Management - Edit", "Redis Management - Delete", "Redis Management - Full Access")
	RequirePermissionManagement    = requirePermissions("Permission Management - View", "Permission Management - Create", "Permission Management - Edit", "Permission Management - Delete", "Permission Management - Full Access", "Admin") // Only for viewing permissions - create/delete done programmatically
	RequireSecurityGroupManagement = requirePermissions("User Management - View", "User Management - Activate", "User Management - Enable", "User Management - Delete", "Admin")
	RequireUserProfilePermission   = requirePermissions("*") // Any authenticated user can access their own profile
)
